# 连续设备扫描测试说明

## 功能概述

`test_continuous_device_scan()` 函数提供了一个连续扫描BLE设备的测试功能，会循环扫描并详细打印所有发现设备的信息。

## 功能特点

### 1. 连续扫描
- 每5秒执行一次扫描
- 每次扫描持续3秒
- 无限循环，直到设备重启

### 2. 详细信息显示
对每个扫描到的设备显示：
- **设备名称**: BLE广告中的设备名
- **MAC地址**: 设备的蓝牙地址
- **信号强度**: RSSI值（dBm）
- **128位UUID**: 设备广告的所有128位服务UUID

### 3. JSON解析
- 解析扫描结果的JSON数据
- 格式化显示设备信息
- 统计扫描到的设备数量

## 使用方法

### 启用测试
在 `main.cc` 中启用：
```cpp
test_continuous_device_scan();
```

### 停止测试
按设备的复位按钮或重新烧录固件

## 输出示例

```
I (1234) MAIN: Starting BLE tests...

========== Scan #1 ==========
I (1235) MAIN: Scan successful: Found 3 devices (total 3)
I (1236) MAIN: Device details (3 devices):
I (1237) MAIN:   Device 1:
I (1238) MAIN:     Name: LinkFragment
I (1239) MAIN:     Address: AA:BB:CC:DD:EE:FF
I (1240) MAIN:     RSSI: -45 dBm
I (1241) MAIN:     128-bit UUIDs (1):
I (1242) MAIN:       UUID 1: 516D52636275695A786B52574D31696845
I (1243) MAIN:     ----
I (1244) MAIN:   Device 2:
I (1245) MAIN:     Name: iPhone
I (1246) MAIN:     Address: 11:22:33:44:55:66
I (1247) MAIN:     RSSI: -67 dBm
I (1248) MAIN:     128-bit UUIDs: none
I (1249) MAIN:     ----
I (1250) MAIN:   Device 3:
I (1251) MAIN:     Name: (no name)
I (1252) MAIN:     Address: 77:88:99:AA:BB:CC
I (1253) MAIN:     RSSI: -78 dBm
I (1254) MAIN:     128-bit UUIDs (2):
I (1255) MAIN:       UUID 1: 123456781234567889ABCDEF01234567
I (1256) MAIN:       UUID 2: FEDCBA9876543210ABCDEF0123456789
I (1257) MAIN:     ----
========== End Scan #1 ==========

I (1258) MAIN: Waiting 5 seconds before next scan...

========== Scan #2 ==========
...
```

## 应用场景

### 1. 设备发现
- 查看附近所有BLE设备
- 监控设备的出现和消失
- 分析设备的信号强度变化

### 2. LinkCID设备监控
- 监控LinkCID分片广播设备
- 查看UUID中的LinkCID片段
- 分析分片轮播模式

### 3. 网络分析
- 分析BLE设备密度
- 监控设备活动模式
- 信号强度分析

## 技术实现

### 扫描参数
- **扫描时间**: 3秒每次
- **扫描间隔**: 5秒
- **过滤器**: 空（扫描所有设备）
- **重复过滤**: 启用

### JSON数据结构
```json
{
  "success": true,
  "count": 3,
  "total_found": 3,
  "uuid_filter": "",
  "devices": [
    {
      "name": "LinkFragment",
      "addr": "AA:BB:CC:DD:EE:FF",
      "rssi": -45,
      "uuids128": ["516D52636275695A786B52574D31696845"]
    }
  ]
}
```

### 错误处理
- JSON解析失败时显示原始数据
- 扫描失败时显示错误信息
- 缺失字段时显示默认值

## 配置选项

### 修改扫描间隔
```cpp
// 修改等待时间（毫秒）
vTaskDelay(pdMS_TO_TICKS(5000));  // 5秒间隔
```

### 修改扫描时间
扫描时间在 `ble_scan_start_and_wait_json()` 函数内部定义为3秒

### 添加过滤器
```cpp
// 只扫描包含特定UUID的设备
std::string scan_result = ble_scan_start_and_wait_json("ABCDEF");
```

## 注意事项

1. **内存使用**: 连续扫描会持续使用内存，长时间运行需要监控
2. **功耗**: 连续扫描会增加功耗
3. **日志量**: 会产生大量日志输出，注意串口缓冲区
4. **设备数量**: 扫描到大量设备时输出会很长

## 与其他测试的配合

- 可以与 `test_scan_detection()` 配合使用
- 一个设备运行连续扫描，另一个设备运行分片广播
- 观察分片轮播在扫描结果中的表现
