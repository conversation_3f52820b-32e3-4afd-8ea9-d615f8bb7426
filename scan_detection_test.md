# 扫描检测测试说明

## 功能概述

添加了扫描检测功能，当设备被其他设备扫描时会在控制台打印"1"字符。

## 测试模式

### 模式1: 分片广播 + 扫描检测
```cpp
test_scan_detection();
```
- 启动LinkCID分片轮播广播
- 当被其他设备扫描时打印"1"
- 适用于作为被扫描的设备

### 模式2: 分片扫描
```cpp
test_linkcid_fragment_scan();
```
- 扫描附近的LinkCID分片设备
- 收集并重组完整的LinkCID
- 适用于作为扫描方的设备

### 模式3: 单设备测试
```cpp
test_linkcid_fragment_advertising();
// 同时运行扫描任务
```
- 同时运行广播和扫描
- 用于单设备功能验证

## 使用方法

### 双设备测试（推荐）

**设备A（被扫描设备）**:
1. 在`main.cc`中启用：`test_scan_detection();`
2. 编译并烧录
3. 观察串口输出，当被扫描时会打印"1"

**设备B（扫描设备）**:
1. 在`main.cc`中启用：`test_linkcid_fragment_scan();`
2. 编译并烧录
3. 观察扫描结果和重组的LinkCID

### 预期输出

**设备A（被扫描设备）输出**:
```
I (1234) MAIN: Starting fragment advertising with scan detection...
I (1235) FRAGMENT_ADV: Fragment advertising started
I (1236) FRAGMENT_ADV: Broadcasting fragment 1/4: 'QmRcbuiZxkRW' (12 chars)
1  <-- 被扫描时打印
I (2236) FRAGMENT_ADV: Broadcasting fragment 2/4: 'M1ihEMh5dfpn' (12 chars)
1  <-- 被扫描时打印
I (3236) FRAGMENT_ADV: Broadcasting fragment 3/4: 'raRMjtubCxv4' (12 chars)
1  <-- 被扫描时打印
I (4236) FRAGMENT_ADV: Broadcasting fragment 4/4: 'WJuBnccrCT' (10 chars)
1  <-- 被扫描时打印
```

**设备B（扫描设备）输出**:
```
I (5000) LINKCID_SCAN: Found fragment from AA:BB:CC:DD:EE:FF: index=0, total_len=46, data='QmRcbuiZxkRW'
I (6000) LINKCID_SCAN: Found fragment from AA:BB:CC:DD:EE:FF: index=1, total_len=46, data='M1ihEMh5dfpn'
I (7000) LINKCID_SCAN: Found fragment from AA:BB:CC:DD:EE:FF: index=2, total_len=46, data='raRMjtubCxv4'
I (8000) LINKCID_SCAN: Found fragment from AA:BB:CC:DD:EE:FF: index=3, total_len=46, data='WJuBnccrCT'
I (8001) FRAGMENT: Completed LinkCID reconstruction: 'QmRcbuiZxkRWM1ihEMh5dfpnraRMjtubCxv4WJuBnccrCT'
```

## 技术实现

### 扫描检测机制
- 在BLE广告事件处理器中监听`BLE_GAP_EVENT_SCAN_REQ_RCVD`事件
- 当收到扫描请求时立即打印"1"字符
- 使用`fflush(stdout)`确保立即输出

### 分片格式（4片）
- 每片最多12字符（受BLE广告包31字节限制）
- 46字符LinkCID分成4片：12+12+12+10
- 格式：`(制造商ID 2字节)(总长度 1字节)(切片index 1字节)(内容 最多12字节)`

## 配置切换

在`main.cc`中修改测试模式：

```cpp
// 模式1: 扫描检测（被扫描设备）
test_scan_detection();

// 模式2: 分片扫描（扫描设备）
// test_linkcid_fragment_scan();

// 模式3: 单设备测试
// test_linkcid_fragment_advertising();
```

## 注意事项

1. **设备角色**: 确保一个设备运行广播模式，另一个运行扫描模式
2. **扫描间隔**: 分片轮播每秒切换一次，扫描需要足够时间收集所有分片
3. **输出格式**: "1"字符直接输出到控制台，便于自动化测试检测
4. **调试信息**: 详细的ESP_LOG信息帮助理解分片收集过程
