# LinkCID分片轮播测试指南

## 功能概述

本实现将46字符的base58编码LinkCID分成3片进行轮播广播，解决了BLE广告包容量限制的问题。

## 分片机制

### 分片格式
每个分片的制造商数据格式：
```
[制造商ID 2字节][LINK标识 4字节][版本 1字节][总长度 1字节][切片index 1字节][内容 N字节]
```

### 分片策略
- **总分片数**: 3片
- **分片大小**: 约15-16字符每片
- **轮播周期**: 每片广播1秒
- **UUID**: 使用固定UUID，不再传输LinkCID信息

## 测试函数

### 1. 分片广播测试
```cpp
test_linkcid_fragment_advertising()
```
- 设置46字符的测试LinkCID
- 启动分片轮播广播
- 每秒切换一个分片，循环进行

### 2. 分片扫描聚合测试
```cpp
test_linkcid_fragment_scan()
```
- 扫描8秒收集分片
- 自动聚合相同设备的分片
- 重组完整LinkCID
- 返回JSON格式结果

### 3. 停止广播测试
```cpp
test_stop_fragment_advertising()
```
- 停止当前的分片轮播

## 使用方法

### 单设备测试
在`main.cc`中已配置为同时运行广播和扫描：
1. 启动分片广播
2. 2秒后开始分片扫描
3. 扫描8秒收集并重组分片

### 双设备测试
**设备A（广播端）**:
```cpp
test_linkcid_fragment_advertising();
```

**设备B（扫描端）**:
```cpp
test_linkcid_fragment_scan();
```

## 输出示例

### 广播端日志
```
I (1234) MAIN: Setting LinkCID: QmRcbuiZxkRWM1ihEMh5dfpnraRMjtubCxv4WJuBnccrCT (46 chars)
I (1235) FRAGMENT_ADV: Fragment advertising started
I (1236) FRAGMENT_ADV: Broadcasting fragment 1/3: 'QmRcbuiZxkRWM1i' (15 chars)
I (2236) FRAGMENT_ADV: Broadcasting fragment 2/3: 'hEMh5dfpnraRMjt' (15 chars)
I (3236) FRAGMENT_ADV: Broadcasting fragment 3/3: 'ubCxv4WJuBnccrCT' (16 chars)
```

### 扫描端日志
```
I (5000) LINKCID_SCAN: Found fragment from AA:BB:CC:DD:EE:FF: index=0, total_len=46, data='QmRcbuiZxkRWM1i'
I (6000) LINKCID_SCAN: Found fragment from AA:BB:CC:DD:EE:FF: index=1, total_len=46, data='hEMh5dfpnraRMjt'
I (7000) LINKCID_SCAN: Found fragment from AA:BB:CC:DD:EE:FF: index=2, total_len=46, data='ubCxv4WJuBnccrCT'
I (7001) FRAGMENT: Completed LinkCID reconstruction: 'QmRcbuiZxkRWM1ihEMh5dfpnraRMjtubCxv4WJuBnccrCT'
```

## 技术细节

### 分片收集算法
1. 按设备地址分组收集分片
2. 验证分片索引和总长度
3. 收集到所有分片后自动重组
4. 清理超时的分片收集（10秒）

### 向后兼容性
- 保持对旧格式的兼容性
- 同时支持UUID传输和分片传输
- 扫描结果中标明数据来源（fragments/legacy）

## 配置参数

在`link_protocol.h`中定义：
```c
#define LINKCID_FRAGMENT_COUNT 3        // 分成3片
#define LINKCID_MAX_LENGTH 46           // 最大46字符
#define LINKCID_FRAGMENT_SIZE 16        // 每片最多16字符
#define LINKCID_MFG_HEADER_SIZE 4       // 制造商数据头部大小
```

在`link_protocol.cc`中定义：
```cpp
static const int FRAGMENT_CYCLE_DURATION_MS = 1000;  // 每片广播1秒
```
