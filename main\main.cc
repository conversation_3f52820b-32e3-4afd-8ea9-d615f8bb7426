#include <esp_log.h>
#include <esp_err.h>
#include <nvs.h>
#include <nvs_flash.h>
#include <driver/gpio.h>
#include "link_protocol.h"

static const char* TAG = "MAIN";

uint8_t ble_addr_type;

// Test function to demonstrate advertising
void test_advertising()
{
    ESP_LOGI(TAG, "Testing BLE advertising...");

    // Example 128-bit UUID: 12345678-1234-5678-9ABC-DEF012345678
    std::string test_uuid = "123456781234567889ABCDEF01234567";
    uint8_t test_version = 2;
    std::string device_name = "LinkPet";

    // Start advertising using C++ convenience function
    int result = ble_start_advertising_cpp(test_uuid, test_version, device_name);
    if (result == 0) {
        ESP_LOGI(TAG, "Advertising started successfully");
    } else {
        ESP_LOGE(TAG, "Failed to start advertising: %d", result);
    }
}

// Test function to demonstrate link cycle
void test_link_cycle()
{
    ESP_LOGI(TAG, "Testing Link Cycle...");

    // Example 128-bit UUID: 12345678-1234-5678-9ABC-DEF012345678
    std::string test_uuid = "ABCDEF0123456789FEDCBA9876543210";
    uint8_t test_version = 3;
    std::string device_name = "LinkCycle";

    // Start link cycle using C++ convenience function
    int result = start_link_cycle_cpp(test_uuid, test_version, device_name);
    if (result == 0) {
        ESP_LOGI(TAG, "Link cycle started successfully");
        ESP_LOGI(TAG, "Link cycle will run with 400ms periods:");
        ESP_LOGI(TAG, "  0-150ms: Scanning for devices with 128-bit UUIDs");
        ESP_LOGI(TAG, "  150-170ms: Random back-off (0-20ms)");
        ESP_LOGI(TAG, "  170-400ms: Advertising with UUID: %s", test_uuid.c_str());
    } else {
        ESP_LOGE(TAG, "Failed to start link cycle: %d", result);
    }
}

// Test function to demonstrate filtered scanning
void test_filtered_scan()
{
    ESP_LOGI(TAG, "Testing filtered BLE scan...");

    // Test with a specific UUID filter
    std::string uuid_filter = "ABCDEF";  // Look for UUIDs containing "ABCDEF"

    ESP_LOGI(TAG, "Starting scan with UUID filter: %s", uuid_filter.c_str());
    std::string scan_result = ble_scan_start_and_wait_json(uuid_filter);
    ESP_LOGI(TAG, "Scan result: %s", scan_result.c_str());
}

// Test function for LinkCID advertising
void test_linkcid_adv()
{
    ESP_LOGI(TAG, "=== Testing LinkCID Advertising ===");

    // Set a test LinkCID
    std::string test_linkcid = "QmRcbuiZxkdfpnraRMjtubCxv4WJuBnccrCT";
    ESP_LOGI(TAG, "Setting LinkCID: %s", test_linkcid.c_str());

    int result = set_linkcid(test_linkcid);
    if (result != 0) {
        ESP_LOGE(TAG, "Failed to set LinkCID: %d", result);
        return;
    }

    // Start advertising with LinkCID
    ESP_LOGI(TAG, "Starting LinkCID advertising...");
    result = ble_start_advertising_cpp("", 1, "LinkCID_Device");
    if (result == 0) {
        ESP_LOGI(TAG, "LinkCID advertising started successfully");
        ESP_LOGI(TAG, "Advertising will continue indefinitely...");
        ESP_LOGI(TAG, "LinkCID is split as:");
        ESP_LOGI(TAG, "  - First 32 chars in 128-bit UUID: %s", test_linkcid.substr(0, 32).c_str());
        ESP_LOGI(TAG, "  - Last 4 chars in Scan Response: %s", test_linkcid.substr(32, 4).c_str());
    } else {
        ESP_LOGE(TAG, "Failed to start LinkCID advertising: %d", result);
    }

    ESP_LOGI(TAG, "=== LinkCID Advertising Active ===");
}

// Test function for LinkCID scanning
void test_linkcid_scan()
{
    ESP_LOGI(TAG, "=== Testing LinkCID Scanning ===");

    // Now scan for LinkCID devices
    ESP_LOGI(TAG, "Starting LinkCID scan for 5 seconds...");
    std::string scan_result = scan_get_linkcid(5000);
    ESP_LOGI(TAG, "LinkCID scan completed");
    ESP_LOGI(TAG, "Scan result JSON: %s", scan_result.c_str());

    ESP_LOGI(TAG, "=== LinkCID Test Complete ===");
}

// Test function for LinkCID fragment advertising (分片广播)
void test_linkcid_fragment_advertising()
{
    ESP_LOGI(TAG, "=== Testing LinkCID Fragment Advertising ===");

    // 设置一个46字符的测试LinkCID
    std::string test_linkcid = "QmRcbuiZxkRWM1ihEMh5dfpnraRMjtubCxv4WJuBnccrCT";
    ESP_LOGI(TAG, "Setting LinkCID: %s (%d chars)", test_linkcid.c_str(), (int)test_linkcid.length());

    int result = set_linkcid(test_linkcid);
    if (result != 0) {
        ESP_LOGE(TAG, "Failed to set LinkCID: %d", result);
        return;
    }

    // 启动分片轮播广播
    ESP_LOGI(TAG, "Starting LinkCID fragment advertising...");
    ESP_LOGI(TAG, "LinkCID will be split into 3 fragments and broadcast in rotation:");
    ESP_LOGI(TAG, "  Fragment 0: chars 0-15");
    ESP_LOGI(TAG, "  Fragment 1: chars 16-31");
    ESP_LOGI(TAG, "  Fragment 2: chars 32-45");
    ESP_LOGI(TAG, "Each fragment will be broadcast for 1 second in a continuous cycle");

    result = start_linkcid_fragment_advertising_cpp();
    if (result == 0) {
        ESP_LOGI(TAG, "Fragment advertising started successfully!");
        ESP_LOGI(TAG, "Broadcasting fragments in rotation...");
        ESP_LOGI(TAG, "Use test_linkcid_fragment_scan() to collect and reconstruct the LinkCID");
    } else {
        ESP_LOGE(TAG, "Failed to start fragment advertising: %d", result);
    }

    ESP_LOGI(TAG, "=== Fragment Advertising Active ===");
}

// Test function for LinkCID fragment scanning and aggregation (扫描分片并聚合)
void test_linkcid_fragment_scan()
{
    ESP_LOGI(TAG, "=== Testing LinkCID Fragment Scanning and Aggregation ===");

    ESP_LOGI(TAG, "Starting fragment scan for 8 seconds...");
    ESP_LOGI(TAG, "This will collect fragments from nearby devices and attempt to reconstruct complete LinkCIDs");

    // 使用新的分片扫描函数，给足够时间收集所有分片
    std::string scan_result = scan_get_linkcid_fragments(8000);

    ESP_LOGI(TAG, "Fragment scan completed");
    ESP_LOGI(TAG, "Scan result JSON: %s", scan_result.c_str());

    ESP_LOGI(TAG, "=== Fragment Scan Complete ===");
}

// Test function to stop fragment advertising
void test_stop_fragment_advertising()
{
    ESP_LOGI(TAG, "=== Stopping Fragment Advertising ===");

    int result = stop_linkcid_fragment_advertising_cpp();
    if (result == 0) {
        ESP_LOGI(TAG, "Fragment advertising stopped successfully");
    } else {
        ESP_LOGE(TAG, "Failed to stop fragment advertising: %d", result);
    }
}

// Test function for scan detection (被扫描检测测试)
void test_scan_detection()
{
    ESP_LOGI(TAG, "=== Testing Scan Detection ===");
    ESP_LOGI(TAG, "Starting fragment advertising with scan detection...");
    ESP_LOGI(TAG, "When other devices scan this device, '1' will be printed to console");

    // 设置LinkCID
    std::string test_linkcid = "QmRcbuiZxkRWM1ihEMh5dfpnraRMjtubCxv4WJuBnccrCT";
    set_linkcid(test_linkcid);

    // 启动分片广播（这会使设备可被扫描）
    int result = start_linkcid_fragment_advertising_cpp();
    if (result == 0) {
        ESP_LOGI(TAG, "Fragment advertising started - device is now scannable");
        ESP_LOGI(TAG, "Watch for '1' characters printed when scanned by other devices");
        ESP_LOGI(TAG, "Use another device to run test_linkcid_fragment_scan() to test");
    } else {
        ESP_LOGE(TAG, "Failed to start fragment advertising: %d", result);
    }
}



/*
 * LinkCID分片轮播测试说明：
 *
 * 1. test_linkcid_fragment_advertising() - 分片广播测试
 *    - 将46字符的LinkCID分成3片进行轮播广播
 *    - 每片广播1秒，循环进行
 *    - 分片格式：(制造商ID)(LINK标识)(版本)(总长度)(切片index)(内容)
 *
 * 2. test_linkcid_fragment_scan() - 扫描分片并聚合测试
 *    - 扫描收集所有相同设备的分片
 *    - 自动重组完整的LinkCID
 *    - 返回JSON格式的结果，包含重组后的完整LinkCID
 *
 * 3. test_stop_fragment_advertising() - 停止分片广播
 *    - 停止当前的分片轮播广播
 *
 * 使用方法：
 * - 在一个设备上运行分片广播
 * - 在另一个设备上运行分片扫描
 * - 或者在同一设备上同时运行（如下面的示例）
 */

extern "C" void app_main(void)
{
    // Initialize the default event loop
    ESP_ERROR_CHECK(esp_event_loop_create_default());

    // Initialize NVS flash for WiFi configuration
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_LOGW(TAG, "Erasing NVS flash to fix corruption");
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);
    nimble_port_init();                             // 2 - Initialize the host and controller stack
    ble_svc_gap_device_name_set("BLE-Scan-Client"); // 3 - Set device name characteristic
    ble_svc_gap_init();                             // 3 - Initialize GAP service

    // Set a custom sync callback that will test LinkCID fragment functionality
    ble_hs_cfg.sync_cb = [](void) {
        ble_hs_id_infer_auto(0, &ble_addr_type);

        // Wait a moment for BLE stack to be ready
        vTaskDelay(pdMS_TO_TICKS(1000));

        // 测试选项 - 选择要运行的测试
        ESP_LOGI(TAG, "Starting LinkCID fragment tests...");

        // 选择测试模式：
        // 模式1: 分片广播 + 扫描检测（当被其他设备扫描时打印"1"）
        test_scan_detection();

        // 模式2: 分片扫描（扫描其他设备的分片）
        // test_linkcid_fragment_scan();

        // 模式3: 同时运行广播和扫描（单设备测试）
        // test_linkcid_fragment_advertising();
        // vTaskDelay(pdMS_TO_TICKS(3000));
        // xTaskCreate([](void* param) {
        //     vTaskDelay(pdMS_TO_TICKS(2000));
        //     test_linkcid_fragment_scan();
        //     vTaskDelete(NULL);
        // }, "fragment_scan_test", 4096, NULL, 5, NULL);


        // 可选：测试其他功能
        // test_linkcid_adv();      // 旧的LinkCID广播方式
        // test_linkcid_scan();     // 旧的LinkCID扫描方式
        // test_advertising();      // 基础广播测试
        // test_link_cycle();       // Link循环测试
        // test_filtered_scan();    // 过滤扫描测试

        // 注意：要停止分片广播，可以调用：
        // test_stop_fragment_advertising();

    };

    nimble_port_freertos_init(host_task);           // 5 - Set infinite task
}
